const API_BASE_URL = 'http://localhost:8001';

interface PerformanceSummary {
  total_realized_pnl: number;
  total_unrealized_pnl: number;
  total_pnl: number;
}

interface Position {
  id: string;
  asset_class: string;
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
  entry_time: string;
  entry_price: number;
  current_price: number;
  unrealized_pnl_local: number;
  unrealized_pnl_base: number;
}

interface Trade {
  id: string;
  asset_class: string;
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
  entry_time: string;
  entry_price: number;
  close_time: string;
  close_price: number;
  realized_pnl_local: number;
  realized_pnl_base: number;
}

interface CreatePositionRequest {
  asset_class: 'fx' | 'crypto' | 'equity' | 'commodity';
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
}

class ApiServiceExtended {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getPerformanceSummary(): Promise<PerformanceSummary> {
    return this.request('/api/dashboard/performance');
  }

  async refreshPrices(): Promise<{ message: string }> {
    return this.request('/api/positions/refresh-prices', {
      method: 'POST',
    });
  }

  async createPosition(request: CreatePositionRequest): Promise<{ message: string; position_id: string }> {
    return this.request('/api/positions/', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getPositions(): Promise<Position[]> {
    return this.request('/api/positions/');
  }

  async getTrades(): Promise<Trade[]> {
    return this.request('/api/trades/');
  }

  async closePosition(positionId: string): Promise<{ message: string; trade_id: string; realized_pnl_base: number }> {
    return this.request('/api/positions/close', {
      method: 'POST',
      body: JSON.stringify({ position_id: positionId }),
    });
  }

  async getEquityCurveData(): Promise<Array<[string, number]>> {
    return this.request('/api/equity-curve-data');
  }
}

export default new ApiServiceExtended();