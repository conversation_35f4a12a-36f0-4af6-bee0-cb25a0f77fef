import { Position, Trade, CreatePositionRequest, PerformanceSummary } from '../types';

const API_BASE_URL = 'http://localhost:8001';

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Position endpoints
  async createPosition(request: CreatePositionRequest): Promise<{ message: string; position_id: string }> {
    return this.request('/api/positions/', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getPositions(): Promise<Position[]> {
    return this.request('/api/positions/');
  }

  async closePosition(positionId: string): Promise<{ message: string; trade_id: string; realized_pnl_base: number }> {
    return this.request('/api/positions/close', {
      method: 'POST',
      body: JSON.stringify({ position_id: positionId }),
    });
  }

  async refreshPrices(): Promise<{ message: string }> {
    return this.request('/api/positions/refresh-prices', {
      method: 'POST',
    });
  }

  // Trade endpoints
  async getTrades(): Promise<Trade[]> {
    return this.request('/api/trades/');
  }

  // Dashboard endpoints
  async getPerformanceSummary(): Promise<PerformanceSummary> {
    return this.request('/api/dashboard/performance');
  }

  // Equity curve endpoint
  async getEquityCurveData(): Promise<Array<[string, number]>> {
    return this.request('/api/equity-curve-data');
  }
}

export default new ApiService();