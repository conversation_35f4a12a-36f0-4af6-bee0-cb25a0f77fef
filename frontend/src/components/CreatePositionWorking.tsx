import React, { useState } from 'react';
import apiService from '../services/apiSimple';
import './CreatePositionForm.css';

interface CreatePositionRequest {
  asset_class: 'fx' | 'crypto' | 'equity' | 'commodity';
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
}

// Extend the simple API service
const extendedApiService = {
  ...apiService,
  async createPosition(request: CreatePositionRequest): Promise<{ message: string; position_id: string }> {
    const response = await fetch('http://localhost:8001/api/positions/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }
};

const CreatePositionWorking: React.FC = () => {
  const [formData, setFormData] = useState<CreatePositionRequest>({
    asset_class: 'fx',
    symbol: 'EURUSD',
    direction: 'buy',
    size: 10000,
  });
  const [loading, setLoading] = useState(false);

  const fxSymbols = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 
    'AUDUSD', 'NZDUSD', 'EURGBP', 'EURJPY', 'EURCHF'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'size' ? Number(value) : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.size <= 0) {
      alert('Position size must be greater than 0');
      return;
    }

    try {
      setLoading(true);
      const response = await extendedApiService.createPosition(formData);
      alert(`Position created successfully! Position ID: ${response.position_id}`);
      
      // Reset form
      setFormData({
        asset_class: 'fx',
        symbol: 'EURUSD',
        direction: 'buy',
        size: 10000,
      });
    } catch (error) {
      console.error('Failed to create position:', error);
      alert('Failed to create position. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-position-container">
      <h3>Create New Position</h3>
      
      <form onSubmit={handleSubmit} className="position-form">
        <div className="form-group">
          <label htmlFor="asset_class">Asset Class</label>
          <select
            id="asset_class"
            name="asset_class"
            value={formData.asset_class}
            onChange={handleInputChange}
            required
          >
            <option value="fx">Foreign Exchange (FX)</option>
            <option value="crypto">Cryptocurrency</option>
            <option value="equity">Equity</option>
            <option value="commodity">Commodity</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="symbol">Symbol</label>
          {formData.asset_class === 'fx' ? (
            <select
              id="symbol"
              name="symbol"
              value={formData.symbol}
              onChange={handleInputChange}
              required
            >
              {fxSymbols.map(symbol => (
                <option key={symbol} value={symbol}>
                  {symbol}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              id="symbol"
              name="symbol"
              value={formData.symbol}
              onChange={handleInputChange}
              placeholder="Enter symbol (e.g., AAPL, BTC, GOLD)"
              required
            />
          )}
        </div>

        <div className="form-group">
          <label htmlFor="direction">Direction</label>
          <select
            id="direction"
            name="direction"
            value={formData.direction}
            onChange={handleInputChange}
            required
          >
            <option value="buy">Buy (Long)</option>
            <option value="sell">Sell (Short)</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="size">Position Size</label>
          <input
            type="number"
            id="size"
            name="size"
            value={formData.size}
            onChange={handleInputChange}
            min="1"
            step="1"
            required
          />
          <small className="form-help">
            {formData.asset_class === 'fx' 
              ? 'FX position size in base currency units' 
              : 'Number of units/shares/contracts'
            }
          </small>
        </div>

        <button 
          type="submit" 
          className="submit-button"
          disabled={loading}
        >
          {loading ? 'Creating Position...' : 'Create Position'}
        </button>
      </form>

      <div className="position-info">
        <h4>Position Information</h4>
        <div className="info-grid">
          <div className="info-item">
            <span className="info-label">Asset Class:</span>
            <span className="info-value">{formData.asset_class.toUpperCase()}</span>
          </div>
          <div className="info-item">
            <span className="info-label">Symbol:</span>
            <span className="info-value">{formData.symbol}</span>
          </div>
          <div className="info-item">
            <span className="info-label">Direction:</span>
            <span className={`info-value direction ${formData.direction}`}>
              {formData.direction.toUpperCase()}
            </span>
          </div>
          <div className="info-item">
            <span className="info-label">Size:</span>
            <span className="info-value">{formData.size.toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePositionWorking;