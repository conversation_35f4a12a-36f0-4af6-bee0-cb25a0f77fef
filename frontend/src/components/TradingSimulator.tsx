import React, { useState } from 'react';
import DashboardWorking from './DashboardWorking';
import CreatePositionWorking from './CreatePositionWorking';
import PositionsWorking from './PositionsWorking';
import EquityCurveBasic from './EquityCurveBasic';
import './TradingSimulator.css';

const TradingSimulator: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'positions' | 'equity-curve' | 'create-position'>('positions');

  return (
    <div className="trading-simulator">
      <DashboardWorking />
      
      <div className="tab-navigation">
        <button 
          className={activeTab === 'positions' ? 'active' : ''}
          onClick={() => setActiveTab('positions')}
        >
          Positions
        </button>
        <button 
          className={activeTab === 'equity-curve' ? 'active' : ''}
          onClick={() => setActiveTab('equity-curve')}
        >
          Equity Curve
        </button>
        <button 
          className={activeTab === 'create-position' ? 'active' : ''}
          onClick={() => setActiveTab('create-position')}
        >
          Create Position
        </button>
      </div>

      <div className="tab-content" style={{ padding: '20px' }}>
        {activeTab === 'positions' && <PositionsWorking />}
        {activeTab === 'equity-curve' && <EquityCurveBasic />}
        {activeTab === 'create-position' && <CreatePositionWorking />}
      </div>
    </div>
  );
};

export default TradingSimulator;