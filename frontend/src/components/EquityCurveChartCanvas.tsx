import React, { useState, useEffect, useRef } from 'react';
import apiService from '../services/api';
import './EquityCurveChart.css';

const EquityCurveChartCanvas: React.FC = () => {
  const [equityData, setEquityData] = useState<Array<[string, number]>>([]);
  const [loading, setLoading] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const loadEquityCurveData = async () => {
    try {
      const data = await apiService.getEquityCurveData();
      setEquityData(data);
    } catch (error) {
      console.error('Failed to load equity curve data:', error);
    }
  };

  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || equityData.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    const { width, height } = canvas;
    const padding = 60;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Get data ranges
    const values = equityData.map(point => point[1]);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue || 1;

    // Draw background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // Draw grid lines
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (i * chartHeight) / 5;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(padding + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 5; i++) {
      const x = padding + (i * chartWidth) / 5;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, padding + chartHeight);
      ctx.stroke();
    }

    // Draw axes
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.stroke();

    // Draw zero line if needed
    if (minValue < 0 && maxValue > 0) {
      const zeroY = padding + chartHeight - ((-minValue) / valueRange) * chartHeight;
      ctx.strokeStyle = '#6b7280';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(padding, zeroY);
      ctx.lineTo(padding + chartWidth, zeroY);
      ctx.stroke();
    }

    // Draw data line
    if (equityData.length > 1) {
      ctx.strokeStyle = values[values.length - 1] >= 0 ? '#22c55e' : '#ef4444';
      ctx.lineWidth = 3;
      ctx.beginPath();

      equityData.forEach((point, index) => {
        const x = padding + (index / (equityData.length - 1)) * chartWidth;
        const y = padding + chartHeight - ((point[1] - minValue) / valueRange) * chartHeight;
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      ctx.stroke();

      // Draw data points
      ctx.fillStyle = '#007bff';
      equityData.forEach((point, index) => {
        const x = padding + (index / (equityData.length - 1)) * chartWidth;
        const y = padding + chartHeight - ((point[1] - minValue) / valueRange) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
      });
    }

    // Draw labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';

    // Y-axis labels
    for (let i = 0; i <= 5; i++) {
      const value = minValue + (i * valueRange) / 5;
      const y = padding + chartHeight - (i * chartHeight) / 5;
      ctx.textAlign = 'right';
      ctx.fillText(`$${value.toFixed(0)}`, padding - 10, y + 4);
    }

    // Title
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('Account Equity Growth Over Time', width / 2, 30);
  };

  useEffect(() => {
    const initializeComponent = async () => {
      setLoading(true);
      await loadEquityCurveData();
      setLoading(false);
    };

    initializeComponent();
  }, []);

  useEffect(() => {
    if (!loading && equityData.length > 0) {
      drawChart();
    }
  }, [equityData, loading]);

  if (loading) {
    return <div className="loading">Loading equity curve...</div>;
  }

  if (equityData.length === 0) {
    return (
      <div className="equity-curve-container">
        <h3>Equity Curve</h3>
        <div className="no-data">
          <p>No trading history available yet.</p>
          <p>Start trading to see your equity curve develop over time.</p>
        </div>
      </div>
    );
  }

  const cumulativePnL = equityData.map(point => point[1]);

  return (
    <div className="equity-curve-container">
      <div className="equity-curve-header">
        <h3>Equity Curve</h3>
        <button 
          className="refresh-button"
          onClick={async () => {
            setLoading(true);
            await loadEquityCurveData();
            setLoading(false);
          }}
          disabled={loading}
        >
          Refresh
        </button>
      </div>
      
      <div className="chart-container">
        <canvas 
          ref={canvasRef}
          style={{ width: '100%', height: '400px', border: '1px solid #e5e7eb' }}
        />
      </div>
      
      <div className="equity-stats">
        <div className="stat-card">
          <h4>Total Trades</h4>
          <p>{equityData.length}</p>
        </div>
        <div className="stat-card">
          <h4>Current P&L</h4>
          <p 
            style={{ 
              color: cumulativePnL[cumulativePnL.length - 1] >= 0 ? '#22c55e' : '#ef4444' 
            }}
          >
            ${cumulativePnL[cumulativePnL.length - 1]?.toFixed(2) || '0.00'}
          </p>
        </div>
        <div className="stat-card">
          <h4>Best Day</h4>
          <p style={{ color: '#22c55e' }}>
            ${Math.max(...cumulativePnL).toFixed(2)}
          </p>
        </div>
        <div className="stat-card">
          <h4>Worst Day</h4>
          <p style={{ color: '#ef4444' }}>
            ${Math.min(...cumulativePnL).toFixed(2)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EquityCurveChartCanvas;
