import React, { useState, useEffect } from 'react';
import apiService from '../services/api';

const EquityCurveBasic: React.FC = () => {
  const [equityData, setEquityData] = useState<Array<[string, number]>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    try {
      setLoading(true);
      const data = await apiService.getEquityCurveData();
      setEquityData(data);
    } catch (err) {
      console.error('Error loading equity data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const renderChart = () => {
    if (equityData.length === 0) {
      return (
        <div style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f8f9fa',
          border: '1px solid #e5e7eb',
          borderRadius: '8px'
        }}>
          <p>No trading data available yet. Create and close some positions to see your equity curve.</p>
        </div>
      );
    }

    const values = equityData.map(point => point[1]);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue || 1;

    const chartWidth = 800;
    const chartHeight = 400;
    const padding = 60;
    const plotWidth = chartWidth - 2 * padding;
    const plotHeight = chartHeight - 2 * padding;

    // Create SVG path for the line
    const pathData = equityData.map((point, index) => {
      const x = padding + (index / (equityData.length - 1)) * plotWidth;
      const y = padding + plotHeight - ((point[1] - minValue) / valueRange) * plotHeight;
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');

    const lineColor = values[values.length - 1] >= 0 ? '#22c55e' : '#ef4444';

    return (
      <div style={{ backgroundColor: 'white', border: '1px solid #e5e7eb', borderRadius: '8px', overflow: 'hidden' }}>
        <svg width={chartWidth} height={chartHeight} style={{ display: 'block' }}>
          {/* Background */}
          <rect width={chartWidth} height={chartHeight} fill="white" />

          {/* Grid lines */}
          {[0, 1, 2, 3, 4, 5].map(i => (
            <g key={`grid-${i}`}>
              {/* Horizontal grid lines */}
              <line
                x1={padding}
                y1={padding + (i * plotHeight) / 5}
                x2={padding + plotWidth}
                y2={padding + (i * plotHeight) / 5}
                stroke="#e5e7eb"
                strokeWidth="1"
              />
              {/* Vertical grid lines */}
              <line
                x1={padding + (i * plotWidth) / 5}
                y1={padding}
                x2={padding + (i * plotWidth) / 5}
                y2={padding + plotHeight}
                stroke="#e5e7eb"
                strokeWidth="1"
              />
            </g>
          ))}

          {/* Axes */}
          <line x1={padding} y1={padding} x2={padding} y2={padding + plotHeight} stroke="#374151" strokeWidth="2" />
          <line x1={padding} y1={padding + plotHeight} x2={padding + plotWidth} y2={padding + plotHeight} stroke="#374151" strokeWidth="2" />

          {/* Zero line if needed */}
          {minValue < 0 && maxValue > 0 && (
            <line
              x1={padding}
              y1={padding + plotHeight - ((-minValue) / valueRange) * plotHeight}
              x2={padding + plotWidth}
              y2={padding + plotHeight - ((-minValue) / valueRange) * plotHeight}
              stroke="#6b7280"
              strokeWidth="2"
              strokeDasharray="5,5"
            />
          )}

          {/* Data line */}
          <path
            d={pathData}
            fill="none"
            stroke={lineColor}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* Data points */}
          {equityData.map((point, index) => {
            const x = padding + (index / (equityData.length - 1)) * plotWidth;
            const y = padding + plotHeight - ((point[1] - minValue) / valueRange) * plotHeight;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="4"
                fill="#007bff"
                stroke="white"
                strokeWidth="2"
              />
            );
          })}

          {/* Y-axis labels */}
          {[0, 1, 2, 3, 4, 5].map(i => {
            const value = minValue + (i * valueRange) / 5;
            const y = padding + plotHeight - (i * plotHeight) / 5;
            return (
              <text
                key={`y-label-${i}`}
                x={padding - 10}
                y={y + 4}
                textAnchor="end"
                fontSize="12"
                fill="#374151"
              >
                ${value.toFixed(0)}
              </text>
            );
          })}

          {/* Title */}
          <text
            x={chartWidth / 2}
            y={30}
            textAnchor="middle"
            fontSize="16"
            fontWeight="bold"
            fill="#1f2937"
          >
            Account Equity Growth Over Time
          </text>
        </svg>
      </div>
    );
  };

  const cumulativePnL = equityData.map(point => point[1]);

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '12px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h3 style={{ margin: 0, color: '#1f2937', fontSize: '20px', fontWeight: '600' }}>Equity Curve</h3>
        <button
          onClick={loadData}
          disabled={loading}
          style={{
            backgroundColor: loading ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '10px 20px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: loading ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.3s ease'
          }}
        >
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {error && <p style={{ color: 'red', marginBottom: '20px' }}>Error: {error}</p>}

      {renderChart()}

      {equityData.length > 0 && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginTop: '24px'
        }}>
          <div style={{ backgroundColor: '#f8f9fa', borderRadius: '8px', padding: '16px', textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px', fontWeight: '500', textTransform: 'uppercase' }}>
              Total Trades
            </h4>
            <p style={{ margin: 0, fontSize: '20px', fontWeight: '700' }}>{equityData.length}</p>
          </div>

          <div style={{ backgroundColor: '#f8f9fa', borderRadius: '8px', padding: '16px', textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px', fontWeight: '500', textTransform: 'uppercase' }}>
              Current P&L
            </h4>
            <p style={{
              margin: 0,
              fontSize: '20px',
              fontWeight: '700',
              color: cumulativePnL[cumulativePnL.length - 1] >= 0 ? '#22c55e' : '#ef4444'
            }}>
              ${cumulativePnL[cumulativePnL.length - 1]?.toFixed(2) || '0.00'}
            </p>
          </div>

          <div style={{ backgroundColor: '#f8f9fa', borderRadius: '8px', padding: '16px', textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px', fontWeight: '500', textTransform: 'uppercase' }}>
              Best Day
            </h4>
            <p style={{ margin: 0, fontSize: '20px', fontWeight: '700', color: '#22c55e' }}>
              ${Math.max(...cumulativePnL).toFixed(2)}
            </p>
          </div>

          <div style={{ backgroundColor: '#f8f9fa', borderRadius: '8px', padding: '16px', textAlign: 'center' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px', fontWeight: '500', textTransform: 'uppercase' }}>
              Worst Day
            </h4>
            <p style={{ margin: 0, fontSize: '20px', fontWeight: '700', color: '#ef4444' }}>
              ${Math.min(...cumulativePnL).toFixed(2)}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EquityCurveBasic;
