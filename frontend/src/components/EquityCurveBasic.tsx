import React, { useState, useEffect } from 'react';
import apiService from '../services/api';

const EquityCurveBasic: React.FC = () => {
  const [equityData, setEquityData] = useState<Array<[string, number]>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('Loading equity curve data...');
        const data = await apiService.getEquityCurveData();
        console.log('Equity data received:', data);
        setEquityData(data);
      } catch (err) {
        console.error('Error loading equity data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
      <h3>Equity Curve - Testing API</h3>

      {loading && <p>Loading data...</p>}
      {error && <p style={{ color: 'red' }}>Error: {error}</p>}

      <div style={{
        height: '300px',
        border: '2px solid #007bff',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        margin: '20px 0',
        flexDirection: 'column'
      }}>
        <p style={{ fontSize: '18px', color: '#007bff' }}>
          Data Points: {equityData.length}
        </p>
        {equityData.length > 0 && (
          <div style={{ fontSize: '14px', color: '#666', textAlign: 'center' }}>
            <p>First: {equityData[0][0]} - ${equityData[0][1]}</p>
            <p>Last: {equityData[equityData.length-1][0]} - ${equityData[equityData.length-1][1]}</p>
          </div>
        )}
      </div>

      <p>API Status: {loading ? 'Loading...' : error ? 'Error' : 'Success'}</p>
    </div>
  );
};

export default EquityCurveBasic;
