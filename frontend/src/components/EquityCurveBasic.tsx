import React from 'react';

const EquityCurveBasic: React.FC = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
      <h3>Equity Curve - Basic Version</h3>
      <p>This is a minimal component to test if the issue is with the component itself.</p>
      <div style={{ 
        height: '300px', 
        border: '2px solid #007bff', 
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        margin: '20px 0'
      }}>
        <p style={{ fontSize: '18px', color: '#007bff' }}>Chart Area - No external dependencies</p>
      </div>
      <p>If you can see this, the component loading works fine.</p>
    </div>
  );
};

export default EquityCurveBasic;
