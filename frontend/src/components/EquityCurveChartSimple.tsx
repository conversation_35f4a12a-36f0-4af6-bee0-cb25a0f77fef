import React, { useState, useEffect } from 'react';

// Try dynamic import for Plotly
const EquityCurveChartSimple: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [PlotComponent, setPlotComponent] = useState<any>(null);

  useEffect(() => {
    // Dynamic import of Plotly to avoid SSR issues
    const loadPlotly = async () => {
      try {
        const Plot = await import('react-plotly.js');
        setPlotComponent(() => Plot.default);
        setLoading(false);
      } catch (err) {
        console.error('Failed to load Plotly:', err);
        setError('Failed to load chart library');
        setLoading(false);
      }
    };

    loadPlotly();
  }, []);

  if (loading) {
    return <div>Loading equity curve...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!PlotComponent) {
    return (
      <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
        <h3>Equity Curve</h3>
        <p>Chart library not available</p>
      </div>
    );
  }

  // Simple test data
  const testData = {
    x: ['2024-01-01', '2024-01-02', '2024-01-03'],
    y: [0, 100, 150],
    type: 'scatter' as const,
    mode: 'lines+markers' as const,
    name: 'Test Data',
  };

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
      <h3>Equity Curve (Testing Plotly)</h3>
      <div style={{ height: '400px', border: '1px solid #ccc' }}>
        <PlotComponent
          data={[testData]}
          layout={{
            title: 'Test Chart',
            xaxis: { title: 'Date' },
            yaxis: { title: 'Value' },
          }}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </div>
  );
};

export default EquityCurveChartSimple;
