import React, { useState, useEffect } from 'react';

const EquityCurveChartSimple: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return <div>Loading equity curve...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div style={{ padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
      <h3>Equity Curve (Simple Version)</h3>
      <p>This is a test version without Plotly to isolate the issue.</p>
      <div style={{ 
        height: '400px', 
        border: '1px solid #ccc', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f9f9f9'
      }}>
        <p>Chart placeholder - Plotly will be added here once we resolve the loading issue</p>
      </div>
    </div>
  );
};

export default EquityCurveChartSimple;
