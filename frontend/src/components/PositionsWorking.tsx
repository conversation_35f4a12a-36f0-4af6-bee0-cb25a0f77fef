import React, { useState, useEffect } from 'react';
import apiService from '../services/apiExtended';
import './PositionsView.css';

interface Position {
  id: string;
  asset_class: string;
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
  entry_time: string;
  entry_price: number;
  current_price: number;
  unrealized_pnl_local: number;
  unrealized_pnl_base: number;
}

interface Trade {
  id: string;
  asset_class: string;
  symbol: string;
  direction: 'buy' | 'sell';
  size: number;
  entry_time: string;
  entry_price: number;
  close_time: string;
  close_price: number;
  realized_pnl_local: number;
  realized_pnl_base: number;
}

const PositionsWorking: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'open' | 'closed'>('open');
  const [positions, setPositions] = useState<Position[]>([]);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(false);

  const loadData = async () => {
    try {
      setLoading(true);
      const [positionsData, tradesData] = await Promise.all([
        apiService.getPositions(),
        apiService.getTrades(),
      ]);
      setPositions(positionsData);
      setTrades(tradesData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClosePosition = async (positionId: string) => {
    try {
      await apiService.closePosition(positionId);
      await loadData(); // Reload data after closing position
      alert('Position closed successfully!');
    } catch (error) {
      console.error('Failed to close position:', error);
      alert('Failed to close position. Please try again.');
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getPnLColor = (value: number) => {
    if (value > 0) return '#22c55e';
    if (value < 0) return '#ef4444';
    return '#6b7280';
  };

  if (loading) {
    return <div className="loading">Loading positions...</div>;
  }

  return (
    <div className="positions-view">
      <div className="positions-tabs">
        <button
          className={activeTab === 'open' ? 'active' : ''}
          onClick={() => setActiveTab('open')}
        >
          Open Positions ({positions.length})
        </button>
        <button
          className={activeTab === 'closed' ? 'active' : ''}
          onClick={() => setActiveTab('closed')}
        >
          Closed Positions ({trades.length})
        </button>
      </div>

      {activeTab === 'open' && (
        <div className="positions-table-container">
          <h3>Open Positions</h3>
          {positions.length === 0 ? (
            <p>No open positions</p>
          ) : (
            <div className="table-wrapper">
              <table className="positions-table">
                <thead>
                  <tr>
                    <th>Asset Class</th>
                    <th>Symbol</th>
                    <th>Direction</th>
                    <th>Size</th>
                    <th>Entry Time</th>
                    <th>Entry Price</th>
                    <th>Current Price</th>
                    <th>Unrealized P&L (Local)</th>
                    <th>Unrealized P&L (Base)</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {positions.map((position) => (
                    <tr key={position.id}>
                      <td>{position.asset_class.toUpperCase()}</td>
                      <td>{position.symbol}</td>
                      <td className={`direction ${position.direction}`}>
                        {position.direction.toUpperCase()}
                      </td>
                      <td>{position.size.toLocaleString()}</td>
                      <td>{formatDateTime(position.entry_time)}</td>
                      <td>{position.entry_price.toFixed(5)}</td>
                      <td>{position.current_price.toFixed(5)}</td>
                      <td style={{ color: getPnLColor(position.unrealized_pnl_local) }}>
                        {formatCurrency(position.unrealized_pnl_local)}
                      </td>
                      <td style={{ color: getPnLColor(position.unrealized_pnl_base) }}>
                        {formatCurrency(position.unrealized_pnl_base)}
                      </td>
                      <td>
                        <button
                          className="close-button"
                          onClick={() => handleClosePosition(position.id)}
                        >
                          Close
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {activeTab === 'closed' && (
        <div className="positions-table-container">
          <h3>Closed Positions</h3>
          {trades.length === 0 ? (
            <p>No closed positions</p>
          ) : (
            <div className="table-wrapper">
              <table className="positions-table">
                <thead>
                  <tr>
                    <th>Asset Class</th>
                    <th>Symbol</th>
                    <th>Direction</th>
                    <th>Size</th>
                    <th>Entry Time</th>
                    <th>Entry Price</th>
                    <th>Close Time</th>
                    <th>Close Price</th>
                    <th>Realized P&L (Local)</th>
                    <th>Realized P&L (Base)</th>
                  </tr>
                </thead>
                <tbody>
                  {trades.map((trade) => (
                    <tr key={trade.id}>
                      <td>{trade.asset_class.toUpperCase()}</td>
                      <td>{trade.symbol}</td>
                      <td className={`direction ${trade.direction}`}>
                        {trade.direction.toUpperCase()}
                      </td>
                      <td>{trade.size.toLocaleString()}</td>
                      <td>{formatDateTime(trade.entry_time)}</td>
                      <td>{trade.entry_price.toFixed(5)}</td>
                      <td>{formatDateTime(trade.close_time)}</td>
                      <td>{trade.close_price.toFixed(5)}</td>
                      <td style={{ color: getPnLColor(trade.realized_pnl_local) }}>
                        {formatCurrency(trade.realized_pnl_local)}
                      </td>
                      <td style={{ color: getPnLColor(trade.realized_pnl_base) }}>
                        {formatCurrency(trade.realized_pnl_base)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PositionsWorking;